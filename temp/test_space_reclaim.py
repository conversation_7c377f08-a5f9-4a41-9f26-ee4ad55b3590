#!/usr/bin/env python3
"""
测试DuckDB空间回收功能的演示脚本
"""

import os
import sys
import tempfile
import shutil

# 添加backend路径
sys.path.append('./backend')

try:
    from database.duckdb_manager import DuckDBManager
except ImportError:
    print("❌ 无法导入DuckDBManager，请确保在项目根目录运行此脚本")
    sys.exit(1)

def format_size(size_bytes):
    """格式化文件大小显示"""
    if size_bytes < 1024:
        return f"{size_bytes} B"
    elif size_bytes < 1024 * 1024:
        return f"{size_bytes / 1024:.2f} KB"
    else:
        return f"{size_bytes / (1024 * 1024):.2f} MB"

def test_space_reclaim():
    """测试空间回收功能"""
    print("🧪 DuckDB空间回收功能测试")
    print("=" * 50)

    # 创建临时数据库在data目录下
    os.makedirs('./data', exist_ok=True)
    test_db_path = './data/test_space_reclaim.duckdb'

    # 如果测试数据库已存在，先删除
    if os.path.exists(test_db_path):
        os.unlink(test_db_path)

    try:
        print(f"📁 创建测试数据库: {test_db_path}")
        db = DuckDBManager(test_db_path)
        
        # 创建测试表并插入大量数据
        print("📊 创建测试表并插入数据...")

        # 先删除可能存在的测试表
        try:
            db.execute_sql("DROP TABLE IF EXISTS test_table")
        except:
            pass

        db.execute_sql("""
            CREATE TABLE test_table (
                id INTEGER,
                data VARCHAR,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 插入10000条测试数据
        for i in range(10000):
            db.execute_sql(
                "INSERT INTO test_table (id, data) VALUES (?, ?)",
                [i, f"测试数据_{i}_" + "x" * 100]  # 每条记录约100字符
            )
        
        # 检查初始大小
        initial_size = os.path.getsize(test_db_path)
        print(f"📏 插入数据后大小: {format_size(initial_size)}")
        
        # 删除大部分数据（模拟清理操作）
        print("🗑️  删除80%的数据...")
        db.execute_sql("DELETE FROM test_table WHERE id % 5 != 0")
        
        # 检查删除后大小（应该没有变化）
        after_delete_size = os.path.getsize(test_db_path)
        print(f"📏 删除数据后大小: {format_size(after_delete_size)}")
        
        if after_delete_size == initial_size:
            print("✅ 确认：DELETE操作不会立即释放磁盘空间")
        
        # 执行CHECKPOINT回收空间
        print("🔄 执行CHECKPOINT回收空间...")
        db.execute_sql("CHECKPOINT")
        
        # 检查CHECKPOINT后大小
        after_checkpoint_size = os.path.getsize(test_db_path)
        print(f"📏 CHECKPOINT后大小: {format_size(after_checkpoint_size)}")
        
        # 计算回收的空间
        reclaimed_bytes = after_delete_size - after_checkpoint_size
        reclaimed_percent = (reclaimed_bytes / after_delete_size) * 100 if after_delete_size > 0 else 0
        
        print(f"💾 回收空间: {format_size(reclaimed_bytes)} ({reclaimed_percent:.1f}%)")
        
        if reclaimed_bytes > 0:
            print("✅ CHECKPOINT成功回收了磁盘空间！")
        else:
            print("⚠️  CHECKPOINT没有回收空间，可能数据已经很紧凑")
        
        # 测试数据库压缩功能
        print("\n🗜️  测试数据库压缩功能...")
        
        # 创建压缩数据库
        compressed_db_path = test_db_path + ".compressed"
        compressed_db = DuckDBManager(compressed_db_path)
        
        # 使用COPY FROM DATABASE压缩
        db.execute_sql(f"ATTACH '{compressed_db_path}' AS compressed_db")
        db.execute_sql("COPY FROM DATABASE main TO compressed_db")
        
        # 关闭连接
        db.close()
        compressed_db.close()
        
        # 比较压缩效果
        compressed_size = os.path.getsize(compressed_db_path)
        compression_saved = after_checkpoint_size - compressed_size
        compression_percent = (compression_saved / after_checkpoint_size) * 100 if after_checkpoint_size > 0 else 0
        
        print(f"📏 压缩后大小: {format_size(compressed_size)}")
        print(f"💾 压缩节省: {format_size(compression_saved)} ({compression_percent:.1f}%)")
        
        if compression_saved > 0:
            print("✅ 数据库压缩进一步节省了空间！")
        else:
            print("ℹ️  数据库已经很紧凑，压缩效果有限")
        
        print("\n📋 测试总结:")
        print(f"  🔸 初始大小: {format_size(initial_size)}")
        print(f"  🔸 删除后大小: {format_size(after_delete_size)}")
        print(f"  🔸 CHECKPOINT后: {format_size(after_checkpoint_size)}")
        print(f"  🔸 压缩后大小: {format_size(compressed_size)}")
        print(f"  🔸 总节省空间: {format_size(initial_size - compressed_size)} ({((initial_size - compressed_size) / initial_size * 100):.1f}%)")
        
        # 清理压缩数据库
        os.unlink(compressed_db_path)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理测试数据库
        if os.path.exists(test_db_path):
            os.unlink(test_db_path)
        print(f"🧹 清理测试文件完成")

if __name__ == "__main__":
    test_space_reclaim()
